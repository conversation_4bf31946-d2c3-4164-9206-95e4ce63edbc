import { useState, useCallback, useMemo, useEffect } from 'react';
import { usePatrimoineCache } from './usePatrimoineCache';
import { useClientContext } from '@/contexts/ClientContext';

interface EvolutionDataPoint {
  date: string; // Format YYYY-MM-DD
  totalPatrimoine: number;
  nombreEntries: number;
  nombreClients: number;
  nombreFournisseurs: number;
  fournisseurDetails: Array<{
    nom: string;
    montant: number;
    color: string;
  }>;
}

interface EvolutionPeriod {
  label: string;
  value: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  groupBy: string; // Format SQL pour GROUP BY
}

interface PatrimoineEvolutionData {
  timeline: EvolutionDataPoint[];
  totalGrowth: number; // Croissance totale en %
  averageGrowth: number; // Croissance moyenne par période
  bestPeriod: EvolutionDataPoint | null;
  worstPeriod: EvolutionDataPoint | null;
  currentTotal: number;
  startDate: Date | null;
  endDate: Date | null;
}

/**
 * Hook pour analyser l'évolution temporelle du patrimoine
 * Gère l'import en masse et l'analyse à partir d'une date donnée
 */
export const usePatrimoineEvolution = () => {
  const { entries, loading } = usePatrimoineCache();
  const { clients } = useClientContext();


  
  const [selectedPeriod, setSelectedPeriod] = useState<EvolutionPeriod['value']>('monthly');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [evolutionData, setEvolutionData] = useState<PatrimoineEvolutionData>({
    timeline: [],
    totalGrowth: 0,
    averageGrowth: 0,
    bestPeriod: null,
    worstPeriod: null,
    currentTotal: 0,
    startDate: null,
    endDate: null
  });

  // Palette de couleurs pour les fournisseurs
  const colors = [
    '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
    '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6B7280'
  ];

  // Périodes disponibles
  const periods: EvolutionPeriod[] = [
    { label: 'Quotidien', value: 'daily', groupBy: 'DATE(created_at)' },
    { label: 'Hebdomadaire', value: 'weekly', groupBy: 'DATE_TRUNC(\'week\', created_at)' },
    { label: 'Mensuel', value: 'monthly', groupBy: 'DATE_TRUNC(\'month\', created_at)' },
    { label: 'Trimestriel', value: 'quarterly', groupBy: 'DATE_TRUNC(\'quarter\', created_at)' }
  ];

  // Filtrer les entrées par date de début si définie
  const filteredEntries = useMemo(() => {
    if (!startDate) return entries;
    return entries.filter(entry => new Date(entry.created_at) >= startDate);
  }, [entries, startDate]);

  // Calculer l'évolution temporelle
  const calculateEvolution = useCallback(async () => {
    // Si pas d'entrées, créer un point de départ avec le total actuel
    if (filteredEntries.length === 0) {
      // Calculer le total actuel depuis toutes les entrées (pas seulement filtrées)
      const totalActuel = entries.reduce((sum, entry) => sum + entry.montant, 0);

      if (totalActuel > 0) {
        // Créer un point de départ avec la date d'aujourd'hui
        const today = new Date();
        const todayKey = today.toISOString().split('T')[0];

        const pointDepart: EvolutionDataPoint = {
          date: todayKey,
          totalPatrimoine: totalActuel,
          nombreEntries: entries.length,
          nombreClients: new Set(entries.map(e => e.client_id)).size,
          nombreFournisseurs: new Set(entries.map(e => e.fournisseur)).size,
          fournisseurDetails: []
        };

        setEvolutionData({
          timeline: [pointDepart],
          totalGrowth: 0,
          averageGrowth: 0,
          bestPeriod: pointDepart,
          worstPeriod: pointDepart,
          currentTotal: totalActuel,
          startDate: today,
          endDate: today
        });
        return;
      }

      // Vraiment aucune donnée
      setEvolutionData({
        timeline: [],
        totalGrowth: 0,
        averageGrowth: 0,
        bestPeriod: null,
        worstPeriod: null,
        currentTotal: 0,
        startDate: null,
        endDate: null
      });
      return;
    }

    // Grouper les entrées par période
    const groupedData = new Map<string, {
      entries: typeof filteredEntries;
      totalPatrimoine: number;
      fournisseurs: Map<string, number>;
    }>();

    filteredEntries.forEach(entry => {
      const date = new Date(entry.created_at);
      let periodKey: string;

      switch (selectedPeriod) {
        case 'daily':
          periodKey = date.toISOString().split('T')[0]; // YYYY-MM-DD
          break;
        case 'weekly':
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          periodKey = weekStart.toISOString().split('T')[0];
          break;
        case 'monthly':
          periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01`;
          break;
        case 'quarterly':
          const quarter = Math.floor(date.getMonth() / 3) + 1;
          periodKey = `${date.getFullYear()}-${String((quarter - 1) * 3 + 1).padStart(2, '0')}-01`;
          break;
        default:
          periodKey = date.toISOString().split('T')[0];
      }

      if (!groupedData.has(periodKey)) {
        groupedData.set(periodKey, {
          entries: [],
          totalPatrimoine: 0,
          fournisseurs: new Map()
        });
      }

      const group = groupedData.get(periodKey)!;
      group.entries.push(entry);
      group.totalPatrimoine += entry.montant;

      const currentFournisseur = group.fournisseurs.get(entry.fournisseur) || 0;
      group.fournisseurs.set(entry.fournisseur, currentFournisseur + entry.montant);
    });

    // Convertir en timeline triée
    let timeline: EvolutionDataPoint[] = Array.from(groupedData.entries())
      .map(([date, data]) => ({
        date,
        totalPatrimoine: data.totalPatrimoine,
        nombreEntries: data.entries.length,
        nombreClients: new Set(data.entries.map(e => e.client_id)).size,
        nombreFournisseurs: data.fournisseurs.size,
        fournisseurDetails: Array.from(data.fournisseurs.entries())
          .map(([nom, montant], index) => ({
            nom,
            montant,
            color: colors[index % colors.length]
          }))
          .sort((a, b) => b.montant - a.montant)
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    // Si on n'a qu'un seul point, créer une évolution naturelle
    if (timeline.length === 1) {
      const singlePoint = timeline[0];
      const pointDate = new Date(singlePoint.date);
      const today = new Date();

      // Si le point unique n'est pas aujourd'hui, ajouter un point aujourd'hui
      if (pointDate.toDateString() !== today.toDateString()) {
        const todayPoint: EvolutionDataPoint = {
          date: today.toISOString().split('T')[0],
          totalPatrimoine: singlePoint.totalPatrimoine, // Même montant pour continuité
          nombreEntries: singlePoint.nombreEntries,
          nombreClients: singlePoint.nombreClients,
          nombreFournisseurs: singlePoint.nombreFournisseurs,
          fournisseurDetails: singlePoint.fournisseurDetails
        };

        timeline.push(todayPoint);
        timeline.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
      }
    }

    // Si pas de timeline du tout, créer un point de départ avec aujourd'hui
    if (timeline.length === 0) {
      const totalActuel = entries.reduce((sum, entry) => sum + entry.montant, 0);
      if (totalActuel > 0) {
        const today = new Date();
        timeline = [{
          date: today.toISOString().split('T')[0],
          totalPatrimoine: totalActuel,
          nombreEntries: entries.length,
          nombreClients: new Set(entries.map(e => e.client_id)).size,
          nombreFournisseurs: new Set(entries.map(e => e.fournisseur)).size,
          fournisseurDetails: []
        }];
      }
    }

    // Calculer les métriques
    const currentTotal = timeline.length > 0 ? timeline[timeline.length - 1].totalPatrimoine : 0;
    const firstTotal = timeline.length > 0 ? timeline[0].totalPatrimoine : 0;
    const totalGrowth = firstTotal > 0 ? ((currentTotal - firstTotal) / firstTotal) * 100 : 0;
    const averageGrowth = timeline.length > 1 ? totalGrowth / (timeline.length - 1) : 0;

    // Trouver les meilleures et pires périodes
    const bestPeriod = timeline.reduce((best, current) => 
      current.totalPatrimoine > best.totalPatrimoine ? current : best, timeline[0]);
    const worstPeriod = timeline.reduce((worst, current) => 
      current.totalPatrimoine < worst.totalPatrimoine ? current : worst, timeline[0]);

    const dates = filteredEntries.map(e => new Date(e.created_at));
    const startDateCalc = dates.length > 0 ? new Date(Math.min(...dates.map(d => d.getTime()))) : null;
    const endDateCalc = dates.length > 0 ? new Date(Math.max(...dates.map(d => d.getTime()))) : null;

    setEvolutionData({
      timeline,
      totalGrowth,
      averageGrowth,
      bestPeriod: timeline.length > 1 ? bestPeriod : null,
      worstPeriod: timeline.length > 1 ? worstPeriod : null,
      currentTotal,
      startDate: startDateCalc,
      endDate: endDateCalc
    });
  }, [filteredEntries, selectedPeriod, entries]); // Ajouter entries pour détecter les changements

  // Recalculer quand les données changent
  useEffect(() => {
    calculateEvolution();
  }, [calculateEvolution]); // Utiliser calculateEvolution comme dépendance

  // Formater les montants
  const formatMontant = useCallback((montant: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(montant);
  }, []);

  // Formater les pourcentages
  const formatPercentage = useCallback((percentage: number) => {
    const sign = percentage >= 0 ? '+' : '';
    return `${sign}${percentage.toFixed(1)}%`;
  }, []);

  // Formater les dates selon la période
  const formatDate = useCallback((dateStr: string) => {
    const date = new Date(dateStr);
    switch (selectedPeriod) {
      case 'daily':
        return date.toLocaleDateString('fr-FR');
      case 'weekly':
        return `Sem. ${date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' })}`;
      case 'monthly':
        return date.toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' });
      case 'quarterly':
        const quarter = Math.floor(date.getMonth() / 3) + 1;
        return `T${quarter} ${date.getFullYear()}`;
      default:
        return date.toLocaleDateString('fr-FR');
    }
  }, [selectedPeriod]);

  // Suggestions de dates de début intelligentes
  const getStartDateSuggestions = useCallback(() => {
    if (entries.length === 0) return [];
    
    const dates = entries.map(e => new Date(e.created_at));
    const oldestDate = new Date(Math.min(...dates.map(d => d.getTime())));
    const now = new Date();

    return [
      { label: 'Depuis le début', value: oldestDate },
      { label: 'Derniers 30 jours', value: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) },
      { label: 'Derniers 90 jours', value: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000) },
      { label: 'Cette année', value: new Date(now.getFullYear(), 0, 1) },
      { label: 'Année dernière', value: new Date(now.getFullYear() - 1, 0, 1) }
    ].filter(suggestion => suggestion.value >= oldestDate);
  }, [entries]);

  return {
    // Données
    evolutionData,
    loading,
    
    // Configuration
    selectedPeriod,
    setSelectedPeriod,
    periods,
    startDate,
    setStartDate,
    getStartDateSuggestions,
    
    // Utilitaires
    formatMontant,
    formatPercentage,
    formatDate,
    
    // État - Accepter même un seul point comme début d'évolution
    hasData: evolutionData.timeline.length > 0
  };
};
