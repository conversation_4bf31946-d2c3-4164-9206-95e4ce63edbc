import { useState, useCallback, useMemo, useEffect } from 'react';
import { useGlobalPatrimoine } from './useGlobalPatrimoine';
import { useClientContext } from '@/contexts/ClientContext';

interface AnalyticsData {
  // Données pour graphique camembert fournisseurs
  fournisseurData: Array<{
    name: string;
    value: number;
    percentage: number;
    color: string;
  }>;
  
  // Données pour graphique barres clients
  clientData: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  
  // Métriques clés
  metrics: {
    totalPatrimoine: number;
    nombreClients: number;
    nombreFournisseurs: number;
    montantMoyen: number;
    fournisseurPrincipal: string;
    clientPrincipal: string;
  };
  
  // Informations temporelles
  temporal: {
    hasTemporalData: boolean;
    oldestEntry: Date | null;
    newestEntry: Date | null;
    totalEntries: number;
  };
}

export const usePatrimoineAnalytics = () => {
  const { clients } = useClientContext();
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [entries, setEntries] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>({
    fournisseurData: [],
    clientData: [],
    metrics: {
      totalPatrimoine: 0,
      nombreClients: 0,
      nombreFournisseurs: 0,
      montantMoyen: 0,
      fournisseurPrincipal: 'Aucun',
      clientPrincipal: 'Aucun'
    },
    temporal: {
      hasTemporalData: false,
      oldestEntry: null,
      newestEntry: null,
      totalEntries: 0
    }
  });

  // Charger les données directement
  const loadPatrimoineData = useCallback(async () => {
    try {
      setLoading(true);
      console.log('🚀 Analytics: Chargement direct des données...');

      // Importer les fonctions nécessaires
      const { supabase } = await import('@/integrations/supabase/client');
      const { safeDecrypt, isEncrypted } = await import('@/utils/encryption');

      const { data: patrimoineData, error } = await supabase
        .from('client_patrimoine')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Déchiffrer les montants
      const decryptedEntries = await Promise.all(
        (patrimoineData || []).map(async (entry) => {
          let montantDecrypted = 0;

          if (entry.montant) {
            if (isEncrypted(entry.montant.toString())) {
              const decrypted = await safeDecrypt(entry.montant.toString());
              montantDecrypted = parseFloat(decrypted) || 0;
            } else {
              montantDecrypted = parseFloat(entry.montant.toString()) || 0;
            }
          }

          return {
            ...entry,
            montant: montantDecrypted
          };
        })
      );

      console.log('✅ Analytics: Données chargées:', decryptedEntries.length, 'entrées');
      console.log('📋 Échantillon des données:', decryptedEntries.slice(0, 3));
      setEntries(decryptedEntries);

    } catch (error) {
      console.error('❌ Analytics: Erreur lors du chargement:', error);
      setEntries([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Palette de couleurs professionnelle
  const colors = [
    '#3B82F6', // Bleu
    '#10B981', // Vert
    '#F59E0B', // Orange
    '#EF4444', // Rouge
    '#8B5CF6', // Violet
    '#06B6D4', // Cyan
    '#84CC16', // Lime
    '#F97316', // Orange foncé
    '#EC4899', // Rose
    '#6B7280'  // Gris
  ];

  // Filtrer les entrées par date si sélectionnée
  const filteredEntries = useMemo(() => {
    if (!selectedDate) return entries;
    return entries.filter(entry => new Date(entry.created_at) >= selectedDate);
  }, [entries, selectedDate]);

  // Calculer les données d'analyse
  const calculateAnalytics = useCallback(async () => {
    if (filteredEntries.length === 0) return;
    // Agrégation par fournisseur
    const fournisseurTotals = new Map<string, number>();
    filteredEntries.forEach(entry => {
      const current = fournisseurTotals.get(entry.fournisseur) || 0;
      fournisseurTotals.set(entry.fournisseur, current + entry.montant);
    });

    // Agrégation par client
    const clientTotals = new Map<string, number>();
    filteredEntries.forEach(entry => {
      const current = clientTotals.get(entry.client_id) || 0;
      clientTotals.set(entry.client_id, current + entry.montant);
    });

    // Total patrimoine
    const totalPatrimoine = filteredEntries.reduce((sum, entry) => sum + entry.montant, 0);

    // Données fournisseurs pour camembert
    const fournisseurData = Array.from(fournisseurTotals.entries())
      .map(([name, value], index) => ({
        name,
        value,
        percentage: totalPatrimoine > 0 ? (value / totalPatrimoine) * 100 : 0,
        color: colors[index % colors.length]
      }))
      .sort((a, b) => b.value - a.value);

    // Données clients pour barres - avec déchiffrement des noms
    const clientData = await Promise.all(
      Array.from(clientTotals.entries()).map(async ([clientId, value], index) => {
        const client = clients.find(c => c.id === clientId);
        let clientName = 'Client inconnu';

        if (client?.name) {
          try {
            // Importer les fonctions de déchiffrement
            const { safeDecrypt, isEncrypted } = await import('@/utils/encryption');

            if (isEncrypted(client.name)) {
              clientName = await safeDecrypt(client.name);
            } else {
              clientName = client.name;
            }
          } catch (error) {
            console.error('Erreur déchiffrement nom client:', error);
            clientName = client.name || 'Client inconnu';
          }
        }

        console.log('📊 Client data:', { clientId, clientName, value, originalName: client?.name });
        return {
          name: clientName,
          value,
          color: colors[index % colors.length]
        };
      })
    );

    // Trier par valeur décroissante
    clientData.sort((a, b) => b.value - a.value);
    console.log('📈 Final clientData:', clientData);

    // Métriques
    const nombreClients = clientTotals.size;
    const nombreFournisseurs = fournisseurTotals.size;
    const montantMoyen = nombreClients > 0 ? totalPatrimoine / nombreClients : 0;
    
    const fournisseurPrincipal = fournisseurData.length > 0 ? fournisseurData[0].name : 'Aucun';
    const clientPrincipal = clientData.length > 0 ? clientData[0].name : 'Aucun';

    // Informations temporelles
    const dates = entries.map(e => new Date(e.created_at));
    const uniqueDates = new Set(dates.map(d => d.toDateString()));
    const hasTemporalData = uniqueDates.size > 1;

    const oldestEntry = dates.length > 0 ? new Date(Math.min(...dates.map(d => d.getTime()))) : null;
    const newestEntry = dates.length > 0 ? new Date(Math.max(...dates.map(d => d.getTime()))) : null;

    const temporal = {
      hasTemporalData,
      oldestEntry,
      newestEntry,
      totalEntries: filteredEntries.length
    };

    // Mettre à jour l'état
    setAnalyticsData({
      fournisseurData,
      clientData,
      metrics: {
        totalPatrimoine,
        nombreClients,
        nombreFournisseurs,
        montantMoyen,
        fournisseurPrincipal,
        clientPrincipal
      },
      temporal
    });
  }, [filteredEntries, clients, colors]);

  // Formater les montants
  const formatMontant = useCallback((montant: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(montant);
  }, []);

  // Formater les pourcentages
  const formatPercentage = useCallback((percentage: number) => {
    return `${percentage.toFixed(1)}%`;
  }, []);

  // Raccourcis de dates
  const getDateShortcuts = useCallback(() => {
    const now = new Date();
    return [
      {
        label: 'Tout',
        value: null
      },
      {
        label: '7 jours',
        value: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      },
      {
        label: '30 jours',
        value: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      },
      {
        label: '3 mois',
        value: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
      }
    ];
  }, []);

  // Charger les données au montage
  useEffect(() => {
    loadPatrimoineData();
  }, [loadPatrimoineData]);

  // Recalculer les analytics quand les données changent
  useEffect(() => {
    calculateAnalytics();
  }, [calculateAnalytics]);

  return {
    // Données
    analyticsData,
    loading,

    // Filtrage temporel
    selectedDate,
    setSelectedDate,
    getDateShortcuts,

    // Utilitaires
    formatMontant,
    formatPercentage,

    // État
    hasData: filteredEntries.length > 0
  };
};
