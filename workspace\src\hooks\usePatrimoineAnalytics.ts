import { useState, useCallback, useMemo } from 'react';
import { useGlobalPatrimoine } from './useGlobalPatrimoine';
import { useClientContext } from '@/contexts/ClientContext';

interface AnalyticsData {
  // Données pour graphique camembert fournisseurs
  fournisseurData: Array<{
    name: string;
    value: number;
    percentage: number;
    color: string;
  }>;
  
  // Données pour graphique barres clients
  clientData: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  
  // Métriques clés
  metrics: {
    totalPatrimoine: number;
    nombreClients: number;
    nombreFournisseurs: number;
    montantMoyen: number;
    fournisseurPrincipal: string;
    clientPrincipal: string;
  };
  
  // Informations temporelles
  temporal: {
    hasTemporalData: boolean;
    oldestEntry: Date | null;
    newestEntry: Date | null;
    totalEntries: number;
  };
}

export const usePatrimoineAnalytics = () => {
  const { entries, loading } = useGlobalPatrimoine();
  const { clients } = useClientContext();
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  // Palette de couleurs professionnelle
  const colors = [
    '#3B82F6', // Bleu
    '#10B981', // Vert
    '#F59E0B', // Orange
    '#EF4444', // Rouge
    '#8B5CF6', // Violet
    '#06B6D4', // Cyan
    '#84CC16', // Lime
    '#F97316', // Orange foncé
    '#EC4899', // Rose
    '#6B7280'  // Gris
  ];

  // Filtrer les entrées par date si sélectionnée
  const filteredEntries = useMemo(() => {
    if (!selectedDate) return entries;
    return entries.filter(entry => new Date(entry.created_at) >= selectedDate);
  }, [entries, selectedDate]);

  // Calculer les données d'analyse
  const analyticsData = useMemo((): AnalyticsData => {
    // Agrégation par fournisseur
    const fournisseurTotals = new Map<string, number>();
    filteredEntries.forEach(entry => {
      const current = fournisseurTotals.get(entry.fournisseur) || 0;
      fournisseurTotals.set(entry.fournisseur, current + entry.montant);
    });

    // Agrégation par client
    const clientTotals = new Map<string, number>();
    filteredEntries.forEach(entry => {
      const current = clientTotals.get(entry.client_id) || 0;
      clientTotals.set(entry.client_id, current + entry.montant);
    });

    // Total patrimoine
    const totalPatrimoine = filteredEntries.reduce((sum, entry) => sum + entry.montant, 0);

    // Données fournisseurs pour camembert
    const fournisseurData = Array.from(fournisseurTotals.entries())
      .map(([name, value], index) => ({
        name,
        value,
        percentage: totalPatrimoine > 0 ? (value / totalPatrimoine) * 100 : 0,
        color: colors[index % colors.length]
      }))
      .sort((a, b) => b.value - a.value);

    // Données clients pour barres
    const clientData = Array.from(clientTotals.entries())
      .map(([clientId, value], index) => {
        const client = clients.find(c => c.id === clientId);
        return {
          name: client?.name || 'Client inconnu',
          value,
          color: colors[index % colors.length]
        };
      })
      .sort((a, b) => b.value - a.value);

    // Métriques
    const nombreClients = clientTotals.size;
    const nombreFournisseurs = fournisseurTotals.size;
    const montantMoyen = nombreClients > 0 ? totalPatrimoine / nombreClients : 0;
    
    const fournisseurPrincipal = fournisseurData.length > 0 ? fournisseurData[0].name : 'Aucun';
    const clientPrincipal = clientData.length > 0 ? clientData[0].name : 'Aucun';

    // Informations temporelles
    const dates = entries.map(e => new Date(e.created_at));
    const uniqueDates = new Set(dates.map(d => d.toDateString()));
    const hasTemporalData = uniqueDates.size > 1;
    
    const oldestEntry = dates.length > 0 ? new Date(Math.min(...dates.map(d => d.getTime()))) : null;
    const newestEntry = dates.length > 0 ? new Date(Math.max(...dates.map(d => d.getTime()))) : null;

    return {
      fournisseurData,
      clientData,
      metrics: {
        totalPatrimoine,
        nombreClients,
        nombreFournisseurs,
        montantMoyen,
        fournisseurPrincipal,
        clientPrincipal
      },
      temporal: {
        hasTemporalData,
        oldestEntry,
        newestEntry,
        totalEntries: filteredEntries.length
      }
    };
  }, [filteredEntries, clients, colors]);

  // Formater les montants
  const formatMontant = useCallback((montant: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(montant);
  }, []);

  // Formater les pourcentages
  const formatPercentage = useCallback((percentage: number) => {
    return `${percentage.toFixed(1)}%`;
  }, []);

  // Raccourcis de dates
  const getDateShortcuts = useCallback(() => {
    const now = new Date();
    return [
      {
        label: 'Tout',
        value: null
      },
      {
        label: '7 jours',
        value: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      },
      {
        label: '30 jours',
        value: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      },
      {
        label: '3 mois',
        value: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
      }
    ];
  }, []);

  return {
    // Données
    analyticsData,
    loading,
    
    // Filtrage temporel
    selectedDate,
    setSelectedDate,
    getDateShortcuts,
    
    // Utilitaires
    formatMontant,
    formatPercentage,
    
    // État
    hasData: filteredEntries.length > 0
  };
};
