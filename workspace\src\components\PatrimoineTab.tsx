import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Plus, 
  Edit3, 
  Trash2, 
  Euro, 
  TrendingUp, 
  Building2,
  Settings
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Client } from '@/types';

interface PatrimoineEntry {
  id: string;
  fournisseur: string;
  montant: number;
  devise: string;
  type_placement?: string;
  notes?: string;
}

interface Fournisseur {
  id: string;
  nom: string;
  actif: boolean;
  ordre_affichage: number;
}

interface PatrimoineTabProps {
  client: Client;
}

export const PatrimoineTab: React.FC<PatrimoineTabProps> = ({ client }) => {
  const [patrimoine, setPatrimoine] = useState<PatrimoineEntry[]>([]);
  const [fournisseurs, setFournisseurs] = useState<Fournisseur[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingEntry, setEditingEntry] = useState<PatrimoineEntry | null>(null);
  const [editingField, setEditingField] = useState<{ id: string; field: string } | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isFournisseurDialogOpen, setIsFournisseurDialogOpen] = useState(false);
  const [newFournisseur, setNewFournisseur] = useState('');

  // Formulaire pour nouvelle entrée
  const [newEntry, setNewEntry] = useState({
    fournisseur: '',
    montant: '',
    devise: 'EUR',
    type_placement: '',
    notes: ''
  });

  // Charger les données
  const loadData = async () => {
    try {
      setLoading(true);

      // Charger le patrimoine du client
      const { data: patrimoineData, error: patrimoineError } = await supabase
        .from('client_patrimoine')
        .select('*')
        .eq('client_id', client.id)
        .order('fournisseur');

      if (patrimoineError) throw patrimoineError;

      // Charger les fournisseurs
      const { data: fournisseursData, error: fournisseursError } = await supabase
        .from('patrimoine_fournisseurs')
        .select('*')
        .eq('actif', true)
        .order('ordre_affichage');

      if (fournisseursError) throw fournisseursError;

      setPatrimoine(patrimoineData || []);
      setFournisseurs(fournisseursData || []);
    } catch (error) {
      console.error('Erreur lors du chargement:', error);
      toast.error('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  // Ajouter une nouvelle entrée
  const addEntry = async () => {
    if (!newEntry.fournisseur || !newEntry.montant) {
      toast.error('Veuillez remplir les champs obligatoires');
      return;
    }

    try {
      const { data, error } = await supabase
        .from('client_patrimoine')
        .insert({
          client_id: client.id,
          fournisseur: newEntry.fournisseur,
          montant: parseFloat(newEntry.montant),
          devise: newEntry.devise,
          type_placement: newEntry.type_placement || null,
          notes: newEntry.notes || null
        })
        .select()
        .single();

      if (error) throw error;

      setPatrimoine(prev => [...prev, data]);
      setNewEntry({
        fournisseur: '',
        montant: '',
        devise: 'EUR',
        type_placement: '',
        notes: ''
      });
      setIsAddDialogOpen(false);
      toast.success('Entrée ajoutée avec succès');

      // Le système temps réel mettra automatiquement à jour les cartes clients
    } catch (error) {
      console.error('Erreur lors de l\'ajout:', error);
      toast.error('Erreur lors de l\'ajout');
    }
  };

  // Modifier une entrée
  const updateEntry = async (id: string, field: string, value: any) => {
    try {
      const { error } = await supabase
        .from('client_patrimoine')
        .update({ [field]: value })
        .eq('id', id);

      if (error) throw error;

      setPatrimoine(prev =>
        prev.map(entry =>
          entry.id === id ? { ...entry, [field]: value } : entry
        )
      );
      toast.success('Entrée mise à jour');

      // Le système temps réel mettra automatiquement à jour les cartes clients
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      toast.error('Erreur lors de la mise à jour');
    }
  };

  // Supprimer une entrée
  const deleteEntry = async (id: string) => {
    try {
      const { error } = await supabase
        .from('client_patrimoine')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setPatrimoine(prev => prev.filter(entry => entry.id !== id));
      toast.success('Entrée supprimée');

      // Le système temps réel mettra automatiquement à jour les cartes clients
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      toast.error('Erreur lors de la suppression');
    }
  };

  // Ajouter un nouveau fournisseur
  const addFournisseur = async () => {
    if (!newFournisseur.trim()) {
      toast.error('Veuillez saisir un nom de fournisseur');
      return;
    }

    try {
      const { data, error } = await supabase
        .from('patrimoine_fournisseurs')
        .insert({
          nom: newFournisseur.trim(),
          ordre_affichage: fournisseurs.length + 1
        })
        .select()
        .single();

      if (error) throw error;

      setFournisseurs(prev => [...prev, data]);
      setNewFournisseur('');
      setIsFournisseurDialogOpen(false);
      toast.success('Fournisseur ajouté');
    } catch (error) {
      console.error('Erreur lors de l\'ajout du fournisseur:', error);
      toast.error('Erreur lors de l\'ajout du fournisseur');
    }
  };

  // Supprimer un fournisseur
  const deleteFournisseur = async (id: string) => {
    try {
      const { error } = await supabase
        .from('patrimoine_fournisseurs')
        .update({ actif: false })
        .eq('id', id);

      if (error) throw error;

      setFournisseurs(prev => prev.filter(f => f.id !== id));
      toast.success('Fournisseur supprimé');
    } catch (error) {
      console.error('Erreur lors de la suppression du fournisseur:', error);
      toast.error('Erreur lors de la suppression du fournisseur');
    }
  };

  // Calculer le total
  const totalPatrimoine = patrimoine.reduce((sum, entry) => {
    if (entry.devise === 'EUR') {
      return sum + entry.montant;
    }
    // Pour d'autres devises, on pourrait ajouter un taux de change
    return sum + entry.montant;
  }, 0);

  // Formater les montants
  const formatMontant = (montant: number, devise: string = 'EUR') => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: devise,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(montant);
  };

  useEffect(() => {
    loadData();
  }, [client.id]);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Chargement du patrimoine...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête avec total et actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-green-600" />
            <h3 className="text-lg font-semibold">Patrimoine Total</h3>
          </div>
          <Badge variant="secondary" className="text-lg px-3 py-1 bg-green-100 text-green-800">
            {formatMontant(totalPatrimoine)}
          </Badge>
        </div>
        
        <div className="flex gap-2">
          <Dialog open={isFournisseurDialogOpen} onOpenChange={setIsFournisseurDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Gérer fournisseurs
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Gérer les fournisseurs</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Ajouter un nouveau fournisseur</label>
                  <Input
                    placeholder="Nom du fournisseur"
                    value={newFournisseur}
                    onChange={(e) => setNewFournisseur(e.target.value)}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Fournisseurs existants</label>
                  <div className="max-h-40 overflow-y-auto space-y-2">
                    {fournisseurs.map((fournisseur) => (
                      <div key={fournisseur.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm">{fournisseur.nom}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                          onClick={() => deleteFournisseur(fournisseur.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsFournisseurDialogOpen(false)}>
                  Fermer
                </Button>
                <Button onClick={addFournisseur} disabled={!newFournisseur.trim()}>
                  Ajouter
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-2" />
                Ajouter une entrée
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Nouvelle entrée patrimoine</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Fournisseur *</label>
                  <Select value={newEntry.fournisseur} onValueChange={(value) => setNewEntry(prev => ({ ...prev, fournisseur: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner un fournisseur" />
                    </SelectTrigger>
                    <SelectContent>
                      {fournisseurs.map((fournisseur) => (
                        <SelectItem key={fournisseur.id} value={fournisseur.nom}>
                          {fournisseur.nom}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <label className="text-sm font-medium mb-2 block">Montant *</label>
                  <Input
                    type="number"
                    placeholder="0"
                    value={newEntry.montant}
                    onChange={(e) => setNewEntry(prev => ({ ...prev, montant: e.target.value }))}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Type de placement</label>
                  <Input
                    placeholder="Ex: Assurance vie, PEA, etc."
                    value={newEntry.type_placement}
                    onChange={(e) => setNewEntry(prev => ({ ...prev, type_placement: e.target.value }))}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Notes</label>
                  <Input
                    placeholder="Notes optionnelles"
                    value={newEntry.notes}
                    onChange={(e) => setNewEntry(prev => ({ ...prev, notes: e.target.value }))}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Annuler
                </Button>
                <Button onClick={addEntry}>
                  Ajouter
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Tableau du patrimoine */}
      <Card className="border-0 shadow-lg bg-white/95 backdrop-blur-sm">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b-2 border-blue-100">
                  <TableHead className="font-semibold text-gray-700 py-4 px-6 w-[200px]">
                    <div className="flex items-center gap-2">
                      <Building2 className="h-4 w-4 text-blue-600" />
                      Fournisseur
                    </div>
                  </TableHead>
                  <TableHead className="font-semibold text-gray-700 text-right py-4 px-6 w-[150px]">
                    <div className="flex items-center justify-end gap-2">
                      <Euro className="h-4 w-4 text-green-600" />
                      Montant
                    </div>
                  </TableHead>
                  <TableHead className="font-semibold text-gray-700 py-4 px-6 w-[150px]">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-purple-600" />
                      Type
                    </div>
                  </TableHead>
                  <TableHead className="font-semibold text-gray-700 py-4 px-6 w-[250px]">Notes</TableHead>
                  <TableHead className="w-[80px] text-center py-4 px-6 font-semibold text-gray-700">Actions</TableHead>
                </TableRow>
              </TableHeader>
            <TableBody>
              {patrimoine.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-12 text-gray-500">
                    <div className="flex flex-col items-center gap-2">
                      <TrendingUp className="h-8 w-8 text-gray-300" />
                      <p>Aucune entrée patrimoine</p>
                      <p className="text-sm">Commencez par ajouter une première entrée</p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                patrimoine.map((entry) => (
                  <TableRow key={entry.id} className="hover:bg-blue-50/50 transition-colors border-b border-gray-100">
                    <TableCell className="font-medium py-4 px-6 text-gray-800">{entry.fournisseur}</TableCell>
                    <TableCell className="text-right py-4 px-6">
                      {editingField?.id === entry.id && editingField?.field === 'montant' ? (
                        <Input
                          type="number"
                          defaultValue={entry.montant.toString()}
                          className="w-28 text-right border-blue-200 focus:border-blue-400"
                          autoFocus
                          onBlur={(e) => {
                            const newValue = parseFloat(e.target.value) || 0;
                            updateEntry(entry.id, 'montant', newValue);
                            setEditingField(null);
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              const newValue = parseFloat(e.currentTarget.value) || 0;
                              updateEntry(entry.id, 'montant', newValue);
                              setEditingField(null);
                            }
                            if (e.key === 'Escape') {
                              setEditingField(null);
                            }
                          }}
                        />
                      ) : (
                        <span
                          className="font-semibold text-green-600 cursor-pointer hover:bg-green-50 px-3 py-2 rounded-lg transition-colors"
                          onClick={() => setEditingField({ id: entry.id, field: 'montant' })}
                        >
                          {formatMontant(entry.montant, entry.devise)}
                        </span>
                      )}
                    </TableCell>
                    <TableCell className="py-4 px-6">
                      {editingField?.id === entry.id && editingField?.field === 'type_placement' ? (
                        <Input
                          defaultValue={entry.type_placement || ''}
                          className="w-36 border-blue-200 focus:border-blue-400"
                          autoFocus
                          onBlur={(e) => {
                            updateEntry(entry.id, 'type_placement', e.target.value);
                            setEditingField(null);
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              updateEntry(entry.id, 'type_placement', e.currentTarget.value);
                              setEditingField(null);
                            }
                            if (e.key === 'Escape') {
                              setEditingField(null);
                            }
                          }}
                        />
                      ) : (
                        <div
                          className="cursor-pointer hover:bg-purple-50 px-3 py-2 rounded-lg min-h-[32px] flex items-center transition-colors"
                          onClick={() => setEditingField({ id: entry.id, field: 'type_placement' })}
                        >
                          {entry.type_placement ? (
                            <Badge variant="outline" className="text-xs bg-purple-50 text-purple-700 border-purple-200">
                              {entry.type_placement}
                            </Badge>
                          ) : (
                            <span className="text-gray-400 text-xs">Cliquer pour ajouter</span>
                          )}
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="py-4 px-6">
                      {editingField?.id === entry.id && editingField?.field === 'notes' ? (
                        <Input
                          defaultValue={entry.notes || ''}
                          className="w-52 border-blue-200 focus:border-blue-400"
                          autoFocus
                          onBlur={(e) => {
                            updateEntry(entry.id, 'notes', e.target.value);
                            setEditingField(null);
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              updateEntry(entry.id, 'notes', e.currentTarget.value);
                              setEditingField(null);
                            }
                            if (e.key === 'Escape') {
                              setEditingField(null);
                            }
                          }}
                        />
                      ) : (
                        <div
                          className="text-sm text-gray-600 cursor-pointer hover:bg-gray-50 px-3 py-2 rounded-lg min-h-[32px] max-w-[220px] truncate flex items-center transition-colors"
                          onClick={() => setEditingField({ id: entry.id, field: 'notes' })}
                        >
                          {entry.notes || <span className="text-gray-400">Cliquer pour ajouter</span>}
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="py-4 px-6">
                      <div className="flex justify-center">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors"
                          onClick={() => deleteEntry(entry.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>


    </div>
  );
};
