import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown,
  Calendar,
  Target,
  BarChart3,
  Clock,
  Award,
  AlertTriangle
} from 'lucide-react';
import { usePatrimoineEvolution } from '@/hooks/usePatrimoineEvolution';

interface PatrimoineDashboardProps {
  isOpen: boolean;
  onClose: () => void;
}

export const PatrimoineDashboard: React.FC<PatrimoineDashboardProps> = ({ isOpen, onClose }) => {
  const {
    evolutionData,
    loading,
    selectedPeriod,
    setSelectedPeriod,
    periods,
    startDate,
    setStartDate,
    getStartDateSuggestions,
    formatMontant,
    formatPercentage,
    formatDate,
    hasData
  } = usePatrimoineEvolution();

  // Tooltip personnalisé pour le graphique
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-800 mb-2">{formatDate(label)}</p>
          <div className="space-y-1">
            <p className="text-green-600 font-medium">
              Total: {formatMontant(data.totalPatrimoine)}
            </p>
            <p className="text-blue-600 text-sm">
              {data.nombreEntries} entrées • {data.nombreClients} clients
            </p>
            <p className="text-purple-600 text-sm">
              {data.nombreFournisseurs} fournisseurs actifs
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[95vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              Évolution du Patrimoine
            </DialogTitle>
          </div>
          <DialogDescription>
            Analyse temporelle de l'évolution du patrimoine avec métriques de performance et tendances.
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
              <p className="text-gray-500">Calcul de l'évolution...</p>
            </div>
          </div>
        ) : !hasData ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <BarChart3 className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <p className="text-lg font-medium text-gray-600 mb-2">Aucune donnée temporelle</p>
              <p className="text-gray-500">Ajoutez des entrées de patrimoine pour voir l'évolution</p>
            </div>
          </div>
        ) : (
          <div className="space-y-6 overflow-y-auto max-h-[80vh] pr-2">
            {/* Contrôles de période et date */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Sélecteur de période */}
              <Card className="border-blue-200 bg-blue-50/30">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Clock className="h-4 w-4 text-blue-600" />
                    Période d'analyse
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex flex-wrap gap-2">
                    {periods.map((period) => (
                      <Button
                        key={period.value}
                        variant={selectedPeriod === period.value ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedPeriod(period.value)}
                        className={selectedPeriod === period.value ? "bg-blue-600 hover:bg-blue-700" : ""}
                      >
                        {period.label}
                      </Button>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Sélecteur de date de début */}
              <Card className="border-purple-200 bg-purple-50/30">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-purple-600" />
                    Date de début
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex flex-wrap gap-2">
                    {getStartDateSuggestions().map((suggestion, index) => (
                      <Button
                        key={index}
                        variant={startDate?.getTime() === suggestion.value.getTime() ? "default" : "outline"}
                        size="sm"
                        onClick={() => setStartDate(suggestion.value)}
                        className={startDate?.getTime() === suggestion.value.getTime() ? "bg-purple-600 hover:bg-purple-700" : ""}
                      >
                        {suggestion.label}
                      </Button>
                    ))}
                  </div>
                  {evolutionData.startDate && evolutionData.endDate && (
                    <p className="text-xs text-gray-600 mt-2">
                      Période: {evolutionData.startDate.toLocaleDateString('fr-FR')} 
                      → {evolutionData.endDate.toLocaleDateString('fr-FR')}
                    </p>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Métriques clés */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card className="border-green-200 bg-green-50/30">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <Target className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <p className="text-xs text-gray-600 font-medium">Total Actuel</p>
                      <p className="text-lg font-bold text-green-700">
                        {formatMontant(evolutionData.currentTotal)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className={`border-${evolutionData.totalGrowth >= 0 ? 'green' : 'red'}-200 bg-${evolutionData.totalGrowth >= 0 ? 'green' : 'red'}-50/30`}>
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 bg-${evolutionData.totalGrowth >= 0 ? 'green' : 'red'}-100 rounded-lg`}>
                      {evolutionData.totalGrowth >= 0 ? (
                        <TrendingUp className="h-5 w-5 text-green-600" />
                      ) : (
                        <TrendingDown className="h-5 w-5 text-red-600" />
                      )}
                    </div>
                    <div>
                      <p className="text-xs text-gray-600 font-medium">Croissance Totale</p>
                      <p className={`text-lg font-bold text-${evolutionData.totalGrowth >= 0 ? 'green' : 'red'}-700`}>
                        {formatPercentage(evolutionData.totalGrowth)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-blue-200 bg-blue-50/30">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <BarChart3 className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-xs text-gray-600 font-medium">Périodes</p>
                      <p className="text-lg font-bold text-blue-700">
                        {evolutionData.timeline.length}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-orange-200 bg-orange-50/30">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <Award className="h-5 w-5 text-orange-600" />
                    </div>
                    <div>
                      <p className="text-xs text-gray-600 font-medium">Meilleure Période</p>
                      <p className="text-sm font-bold text-orange-700">
                        {evolutionData.bestPeriod ? formatMontant(evolutionData.bestPeriod.totalPatrimoine) : '-'}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Graphique principal d'évolution */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                  Évolution du Patrimoine Total
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-96">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={evolutionData.timeline}>
                      <defs>
                        <linearGradient id="patrimoineGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#10B981" stopOpacity={0.3}/>
                          <stop offset="95%" stopColor="#10B981" stopOpacity={0}/>
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="date" 
                        tickFormatter={formatDate}
                        tick={{ fontSize: 12 }}
                      />
                      <YAxis 
                        tickFormatter={(value) => formatMontant(value)}
                        tick={{ fontSize: 12 }}
                      />
                      <Tooltip content={<CustomTooltip />} />
                      <Area
                        type="monotone"
                        dataKey="totalPatrimoine"
                        stroke="#10B981"
                        strokeWidth={3}
                        fill="url(#patrimoineGradient)"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Analyse des performances */}
            {evolutionData.bestPeriod && evolutionData.worstPeriod && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="border-green-200 bg-green-50/30">
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Award className="h-5 w-5 text-green-600" />
                      Meilleure Performance
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <p className="text-sm text-gray-600">
                        {formatDate(evolutionData.bestPeriod.date)}
                      </p>
                      <p className="text-2xl font-bold text-green-700">
                        {formatMontant(evolutionData.bestPeriod.totalPatrimoine)}
                      </p>
                      <div className="flex gap-4 text-sm text-gray-600">
                        <span>{evolutionData.bestPeriod.nombreEntries} entrées</span>
                        <span>{evolutionData.bestPeriod.nombreClients} clients</span>
                        <span>{evolutionData.bestPeriod.nombreFournisseurs} fournisseurs</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-orange-200 bg-orange-50/30">
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <AlertTriangle className="h-5 w-5 text-orange-600" />
                      Performance Minimale
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <p className="text-sm text-gray-600">
                        {formatDate(evolutionData.worstPeriod.date)}
                      </p>
                      <p className="text-2xl font-bold text-orange-700">
                        {formatMontant(evolutionData.worstPeriod.totalPatrimoine)}
                      </p>
                      <div className="flex gap-4 text-sm text-gray-600">
                        <span>{evolutionData.worstPeriod.nombreEntries} entrées</span>
                        <span>{evolutionData.worstPeriod.nombreClients} clients</span>
                        <span>{evolutionData.worstPeriod.nombreFournisseurs} fournisseurs</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
