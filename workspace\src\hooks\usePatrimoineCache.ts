import { useState, useCallback, useRef, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { safeDecrypt, isEncrypted } from '@/utils/encryption';
import { useSupabaseSubscription, onInsert, onUpdate, onDelete } from './useSupabaseSubscription';

interface PatrimoineEntry {
  id: string;
  client_id: string;
  fournisseur: string;
  montant: number;
  devise: string;
  created_at: string;
  updated_at: string;
}

interface FournisseurData {
  id: string;
  nom: string;
  ordre_affichage: number;
  actif: boolean;
}

interface PatrimoineCacheData {
  entries: PatrimoineEntry[];
  fournisseurs: FournisseurData[];
  lastUpdated: number;
  loading: boolean;
}

const CACHE_DURATION = 30000; // 30 secondes

/**
 * Hook pour gérer le cache des données patrimoine
 * Évite les requêtes redondantes et améliore les performances
 */
export const usePatrimoineCache = () => {
  const [cache, setCache] = useState<PatrimoineCacheData>({
    entries: [],
    fournisseurs: [],
    lastUpdated: 0,
    loading: false
  });

  const loadingRef = useRef(false);

  // Vérifier si le cache est valide
  const isCacheValid = useCallback(() => {
    const now = Date.now();
    return (now - cache.lastUpdated) < CACHE_DURATION && cache.entries.length > 0;
  }, [cache.lastUpdated, cache.entries.length]);

  // Charger les données avec cache intelligent
  const loadPatrimoineData = useCallback(async (forceRefresh = false) => {
    // Accéder au cache actuel
    const currentCache = cache;
    const isValid = isCacheValid();

    // Si le cache est valide et qu'on ne force pas le refresh, retourner le cache
    if (!forceRefresh && isValid) {
      console.log('📦 Cache patrimoine valide, utilisation du cache');
      return currentCache;
    }

    // Éviter les requêtes multiples simultanées
    if (loadingRef.current) {
      console.log('⏳ Chargement déjà en cours, attente...');
      return currentCache;
    }

    try {
      loadingRef.current = true;
      setCache(prev => ({ ...prev, loading: true }));

      console.log('🔄 Rechargement des données patrimoine...');

      // Charger en parallèle les entrées et les fournisseurs
      const [patrimoineResponse, fournisseursResponse] = await Promise.all([
        supabase
          .from('client_patrimoine')
          .select('*')
          .order('created_at', { ascending: false }),
        supabase
          .from('patrimoine_fournisseurs')
          .select('*')
          .eq('actif', true)
          .order('ordre_affichage')
      ]);

      if (patrimoineResponse.error) throw patrimoineResponse.error;
      if (fournisseursResponse.error) throw fournisseursResponse.error;

      // Déchiffrer les montants en parallèle
      const decryptedEntries = await Promise.all(
        (patrimoineResponse.data || []).map(async (entry) => {
          let montantDecrypted = 0;

          if (entry.montant) {
            if (isEncrypted(entry.montant.toString())) {
              const decrypted = await safeDecrypt(entry.montant.toString());
              montantDecrypted = parseFloat(decrypted) || 0;
            } else {
              montantDecrypted = parseFloat(entry.montant.toString()) || 0;
            }
          }

          return {
            ...entry,
            montant: montantDecrypted
          };
        })
      );

      const newCache = {
        entries: decryptedEntries,
        fournisseurs: fournisseursResponse.data || [],
        lastUpdated: Date.now(),
        loading: false
      };

      setCache(newCache);
      console.log(`✅ Cache patrimoine mis à jour: ${decryptedEntries.length} entrées, ${fournisseursResponse.data?.length || 0} fournisseurs`);
      console.log('🔍 DIAGNOSTIC Cache - Dates des entrées:', decryptedEntries.map(e => e.created_at).slice(0, 5));
      console.log('🔍 DIAGNOSTIC Cache - Exemple d\'entrée:', decryptedEntries[0]);

      return newCache;

    } catch (error) {
      console.error('❌ Erreur lors du chargement patrimoine:', error);
      setCache(prev => ({ ...prev, loading: false }));
      return currentCache;
    } finally {
      loadingRef.current = false;
    }
  }, [cache, isCacheValid]); // Garder les dépendances nécessaires

  // Invalider le cache (après une modification)
  const invalidateCache = useCallback(() => {
    console.log('🗑️ Invalidation du cache patrimoine');
    setCache(prev => ({ ...prev, lastUpdated: 0 }));
  }, []);

  // Mettre à jour une entrée dans le cache
  const updateCacheEntry = useCallback((entryId: string, newMontant: number) => {
    setCache(prev => ({
      ...prev,
      entries: prev.entries.map(entry =>
        entry.id === entryId
          ? { ...entry, montant: newMontant }
          : entry
      )
    }));
  }, []);

  // Ajouter une entrée au cache
  const addCacheEntry = useCallback((newEntry: PatrimoineEntry) => {
    setCache(prev => ({
      ...prev,
      entries: [newEntry, ...prev.entries]
    }));
  }, []);

  // Supprimer une entrée du cache
  const removeCacheEntry = useCallback((entryId: string) => {
    setCache(prev => ({
      ...prev,
      entries: prev.entries.filter(entry => entry.id !== entryId)
    }));
  }, []);

  // 🚀 TEMPS RÉEL : Invalider le cache automatiquement sur les changements
  const handleCacheInvalidation = useCallback(() => {
    console.log('🔄 Cache: Changement détecté, invalidation...');
    invalidateCache();
    // Recharger immédiatement pour avoir les données fraîches
    loadPatrimoineData(true);
  }, [invalidateCache, loadPatrimoineData]);

  useSupabaseSubscription(
    'patrimoine-cache-invalidation',
    [
      // Nouvelle entrée patrimoine → Invalider le cache
      onInsert('client_patrimoine', handleCacheInvalidation),
      // Entrée patrimoine modifiée → Invalider le cache
      onUpdate('client_patrimoine', handleCacheInvalidation),
      // Entrée patrimoine supprimée → Invalider le cache
      onDelete('client_patrimoine', handleCacheInvalidation),
      // Nouveau fournisseur → Invalider le cache
      onInsert('patrimoine_fournisseurs', handleCacheInvalidation),
      // Fournisseur modifié → Invalider le cache
      onUpdate('patrimoine_fournisseurs', handleCacheInvalidation),
      // Fournisseur supprimé → Invalider le cache
      onDelete('patrimoine_fournisseurs', handleCacheInvalidation)
    ]
  );

  return {
    // Données
    entries: cache.entries,
    fournisseurs: cache.fournisseurs,
    loading: cache.loading,

    // Actions
    loadPatrimoineData,
    invalidateCache,
    updateCacheEntry,
    addCacheEntry,
    removeCacheEntry,

    // État
    isCacheValid: isCacheValid(),
    lastUpdated: cache.lastUpdated
  };
};
