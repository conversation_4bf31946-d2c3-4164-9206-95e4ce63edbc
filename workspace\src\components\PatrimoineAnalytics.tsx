import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from 'recharts';
import { 
  BarChart3, 
  <PERSON><PERSON><PERSON> as PieChartIcon,
  TrendingUp,
  Users,
  Building2,
  Euro,
  Calendar,
  Target
} from 'lucide-react';
import { usePatrimoineAnalytics } from '@/hooks/usePatrimoineAnalytics';

interface PatrimoineAnalyticsProps {
  isOpen: boolean;
  onClose: () => void;
}

export const PatrimoineAnalytics: React.FC<PatrimoineAnalyticsProps> = ({ isOpen, onClose }) => {
  const {
    analyticsData,
    loading,
    selectedDate,
    setSelectedDate,
    getDateShortcuts,
    formatMontant,
    formatPercentage,
    hasData
  } = usePatrimoineAnalytics();

  // Tooltip personnalisé pour le camembert
  const CustomPieTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-800">{data.name}</p>
          <p className="text-green-600">{formatMontant(data.value)}</p>
          <p className="text-blue-600">{formatPercentage(data.percentage)}</p>
        </div>
      );
    }
    return null;
  };

  // Tooltip personnalisé pour les barres
  const CustomBarTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-800">{label}</p>
          <p className="text-green-600">{formatMontant(payload[0].value)}</p>
        </div>
      );
    }
    return null;
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[95vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              Analyse Approfondie du Patrimoine
            </DialogTitle>
            <Badge variant="secondary" className="text-sm px-3 py-1 bg-blue-100 text-blue-800">
              {analyticsData.temporal.totalEntries} entrées analysées
            </Badge>
          </div>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-500">Chargement des analyses...</p>
            </div>
          </div>
        ) : !hasData ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <BarChart3 className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <p className="text-lg font-medium text-gray-600 mb-2">Aucune donnée à analyser</p>
              <p className="text-gray-500">Ajoutez des entrées de patrimoine pour voir les analyses</p>
            </div>
          </div>
        ) : (
          <div className="space-y-6 overflow-y-auto max-h-[80vh] pr-2">
            {/* Filtres temporels */}
            {analyticsData.temporal.hasTemporalData && (
              <Card className="border-blue-200 bg-blue-50/30">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-blue-600" />
                    Période d'analyse
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex flex-wrap gap-2">
                    {getDateShortcuts().map((shortcut) => (
                      <Button
                        key={shortcut.label}
                        variant={selectedDate === shortcut.value ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedDate(shortcut.value)}
                        className={selectedDate === shortcut.value ? "bg-blue-600 hover:bg-blue-700" : ""}
                      >
                        {shortcut.label}
                      </Button>
                    ))}
                  </div>
                  {analyticsData.temporal.oldestEntry && analyticsData.temporal.newestEntry && (
                    <p className="text-xs text-gray-600 mt-2">
                      Données disponibles du {analyticsData.temporal.oldestEntry.toLocaleDateString('fr-FR')} 
                      au {analyticsData.temporal.newestEntry.toLocaleDateString('fr-FR')}
                    </p>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Métriques clés */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card className="border-green-200 bg-green-50/30">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <Euro className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <p className="text-xs text-gray-600 font-medium">Total Patrimoine</p>
                      <p className="text-lg font-bold text-green-700">
                        {formatMontant(analyticsData.metrics.totalPatrimoine)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-blue-200 bg-blue-50/30">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Users className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-xs text-gray-600 font-medium">Clients Actifs</p>
                      <p className="text-lg font-bold text-blue-700">
                        {analyticsData.metrics.nombreClients}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-purple-200 bg-purple-50/30">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <Building2 className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-xs text-gray-600 font-medium">Fournisseurs</p>
                      <p className="text-lg font-bold text-purple-700">
                        {analyticsData.metrics.nombreFournisseurs}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-orange-200 bg-orange-50/30">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <Target className="h-5 w-5 text-orange-600" />
                    </div>
                    <div>
                      <p className="text-xs text-gray-600 font-medium">Montant Moyen</p>
                      <p className="text-lg font-bold text-orange-700">
                        {formatMontant(analyticsData.metrics.montantMoyen)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Graphiques principaux */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Répartition par fournisseur */}
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <PieChartIcon className="h-5 w-5 text-blue-600" />
                    Répartition par Fournisseur
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={analyticsData.fournisseurData}
                          cx="50%"
                          cy="50%"
                          outerRadius={100}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percentage }) => `${name} (${formatPercentage(percentage)})`}
                          labelLine={false}
                        >
                          {analyticsData.fournisseurData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip content={<CustomPieTooltip />} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              {/* Comparaison par client */}
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <BarChart3 className="h-5 w-5 text-green-600" />
                    Patrimoine par Client
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={analyticsData.clientData}
                        layout="horizontal"
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis type="number" tickFormatter={(value) => formatMontant(value)} />
                        <YAxis dataKey="name" type="category" width={120} />
                        <Tooltip content={<CustomBarTooltip />} />
                        <Bar dataKey="value" fill="#10B981" radius={[0, 4, 4, 0]} />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Top performers */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="border-green-200 bg-green-50/30">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-green-600" />
                    Top Fournisseurs
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analyticsData.fournisseurData.slice(0, 5).map((fournisseur, index) => (
                      <div key={fournisseur.name} className="flex items-center justify-between p-3 bg-white rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center justify-center w-6 h-6 rounded-full bg-green-100 text-green-700 text-sm font-bold">
                            {index + 1}
                          </div>
                          <span className="font-medium">{fournisseur.name}</span>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-green-700">{formatMontant(fournisseur.value)}</p>
                          <p className="text-xs text-gray-500">{formatPercentage(fournisseur.percentage)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="border-blue-200 bg-blue-50/30">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Users className="h-5 w-5 text-blue-600" />
                    Top Clients
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analyticsData.clientData.slice(0, 5).map((client, index) => (
                      <div key={client.name} className="flex items-center justify-between p-3 bg-white rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-700 text-sm font-bold">
                            {index + 1}
                          </div>
                          <span className="font-medium">{client.name}</span>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-blue-700">{formatMontant(client.value)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
