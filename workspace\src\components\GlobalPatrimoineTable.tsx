import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  TrendingUp,
  Building2,
  Euro,
  X,
  Plus,
  Edit3,
  Settings,
  Trash2,
  BarChart3
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useClientContext } from '@/contexts/ClientContext';
import { useGlobalPatrimoine } from '@/hooks/useGlobalPatrimoine';
import { usePatrimoineCache } from '@/hooks/usePatrimoineCache';
import { PatrimoineAnalytics } from './PatrimoineAnalytics';
import { PatrimoineDashboard } from './PatrimoineDashboard';

interface PatrimoineMatrixEntry {
  clientId: string;
  clientName: string;
  fournisseurId: string;
  fournisseurNom: string;
  montant: number;
  entryId?: string; // ID de l'entrée en base si elle existe
}

interface MatrixData {
  clients: Array<{
    id: string;
    name: string;
    total: number;
  }>;
  fournisseurs: Array<{
    id: string;
    nom: string;
    total: number;
  }>;
  matrix: Map<string, Map<string, PatrimoineMatrixEntry>>; // clientId -> fournisseurId -> entry
  grandTotal: number;
}

interface GlobalPatrimoineTableProps {
  isOpen: boolean;
  onClose: () => void;
}

export const GlobalPatrimoineTable: React.FC<GlobalPatrimoineTableProps> = ({ isOpen, onClose }) => {
  const { clients } = useClientContext();
  const {
    entries,
    loading,
    loadAllPatrimoine,
    upsertPatrimoineEntry,
    getClientTotals,
    getFournisseurTotals,
    getGrandTotal
  } = useGlobalPatrimoine();

  const {
    fournisseurs: cachedFournisseurs,
    loadPatrimoineData: loadCachedData
  } = usePatrimoineCache();

  const [matrixData, setMatrixData] = useState<MatrixData>({
    clients: [],
    fournisseurs: [],
    matrix: new Map(),
    grandTotal: 0
  });
  const [editingCell, setEditingCell] = useState<{ clientId: string; fournisseurId: string } | null>(null);
  const [editingValue, setEditingValue] = useState<string>('');
  const [showFournisseurManager, setShowFournisseurManager] = useState(false);
  const [newFournisseurName, setNewFournisseurName] = useState('');
  const [editingFournisseur, setEditingFournisseur] = useState<{ id: string; nom: string } | null>(null);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [showDashboard, setShowDashboard] = useState(false);

  // Construire la matrice à partir des données du hook (optimisé avec cache)
  const buildMatrix = useCallback(async () => {
    try {
      // 1. Utiliser les fournisseurs du cache si disponibles
      let fournisseursData = cachedFournisseurs;

      // Si pas de fournisseurs en cache, charger depuis la base
      if (!fournisseursData || fournisseursData.length === 0) {
        console.log('🔄 Chargement fournisseurs depuis la base...');
        await loadCachedData(false); // Utilise le cache si valide
        fournisseursData = cachedFournisseurs;
      }

      // 2. Construire la matrice
      const matrix = new Map<string, Map<string, PatrimoineMatrixEntry>>();
      const clientTotals = getClientTotals();
      const fournisseurTotals = getFournisseurTotals();
      const grandTotal = getGrandTotal();

      // Initialiser la matrice pour tous les clients
      clients.forEach(client => {
        matrix.set(client.id, new Map());
      });

      // Remplir la matrice avec les données existantes
      entries.forEach(entry => {
        const clientMap = matrix.get(entry.client_id);
        if (clientMap) {
          // Trouver le fournisseur correspondant
          const fournisseur = fournisseursData?.find(f => f.nom === entry.fournisseur);
          if (fournisseur) {
            const matrixEntry: PatrimoineMatrixEntry = {
              clientId: entry.client_id,
              clientName: clients.find(c => c.id === entry.client_id)?.name || 'Client inconnu',
              fournisseurId: fournisseur.id,
              fournisseurNom: fournisseur.nom,
              montant: entry.montant,
              entryId: entry.id
            };

            clientMap.set(fournisseur.id, matrixEntry);
          }
        }
      });

      // 3. Préparer les données finales
      const clientsWithTotals = clients.map(client => ({
        id: client.id,
        name: client.name,
        total: clientTotals.get(client.id) || 0
      }));

      const fournisseursWithTotals = (fournisseursData || []).map(fournisseur => ({
        id: fournisseur.id,
        nom: fournisseur.nom,
        total: fournisseurTotals.get(fournisseur.nom) || 0
      }));

      setMatrixData({
        clients: clientsWithTotals,
        fournisseurs: fournisseursWithTotals,
        matrix,
        grandTotal
      });

    } catch (error) {
      console.error('Erreur lors de la construction de la matrice:', error);
      toast.error('Erreur lors du chargement des données');
    }
  }, [cachedFournisseurs, loadCachedData, entries, clients, getClientTotals, getFournisseurTotals, getGrandTotal]);

  // Formater les montants (optimisé avec useMemo)
  const formatMontant = useMemo(() => {
    const formatter = new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    });

    return (montant: number) => {
      if (montant === 0) return '-';
      return formatter.format(montant);
    };
  }, []);

  // Obtenir la valeur d'une cellule
  const getCellValue = (clientId: string, fournisseurId: string): number => {
    return matrixData.matrix.get(clientId)?.get(fournisseurId)?.montant || 0;
  };

  // Gérer l'édition d'une cellule
  const handleCellEdit = (clientId: string, fournisseurId: string, currentValue: number) => {
    setEditingCell({ clientId, fournisseurId });
    // Si la valeur est 0, on affiche un champ vide pour gagner du temps
    setEditingValue(currentValue === 0 ? '' : currentValue.toString());
  };

  // Sauvegarder l'édition
  const handleSaveEdit = async () => {
    if (!editingCell) return;

    const newValue = parseFloat(editingValue) || 0;
    const fournisseurNom = matrixData.fournisseurs.find(f => f.id === editingCell.fournisseurId)?.nom;

    if (fournisseurNom) {
      const success = await upsertPatrimoineEntry(
        editingCell.clientId,
        fournisseurNom,
        newValue
      );

      if (success) {
        // Reconstruire la matrice avec les nouvelles données
        await buildMatrix();
      }
    }

    setEditingCell(null);
    setEditingValue('');
  };

  // Annuler l'édition
  const handleCancelEdit = () => {
    setEditingCell(null);
    setEditingValue('');
  };

  // Ajouter un nouveau fournisseur
  const handleAddFournisseur = async () => {
    if (!newFournisseurName.trim()) return;

    try {
      // Obtenir le prochain ordre d'affichage
      const { data: existingFournisseurs } = await supabase
        .from('patrimoine_fournisseurs')
        .select('ordre_affichage')
        .order('ordre_affichage', { ascending: false })
        .limit(1);

      const nextOrder = (existingFournisseurs?.[0]?.ordre_affichage || 0) + 1;

      const { error } = await supabase
        .from('patrimoine_fournisseurs')
        .insert({
          nom: newFournisseurName.trim(),
          ordre_affichage: nextOrder
        });

      if (error) throw error;

      toast.success('Fournisseur ajouté');
      setNewFournisseurName('');
      await buildMatrix(); // Reconstruire la matrice

    } catch (error) {
      console.error('Erreur lors de l\'ajout du fournisseur:', error);
      toast.error('Erreur lors de l\'ajout');
    }
  };

  // Supprimer un fournisseur
  const handleDeleteFournisseur = async (fournisseurId: string, fournisseurNom: string) => {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer le fournisseur "${fournisseurNom}" ?\n\nToutes les entrées de patrimoine associées seront également supprimées.`)) {
      return;
    }

    try {
      // Supprimer d'abord toutes les entrées de patrimoine associées
      const { error: patrimoineError } = await supabase
        .from('client_patrimoine')
        .delete()
        .eq('fournisseur', fournisseurNom);

      if (patrimoineError) throw patrimoineError;

      // Puis supprimer le fournisseur
      const { error: fournisseurError } = await supabase
        .from('patrimoine_fournisseurs')
        .delete()
        .eq('id', fournisseurId);

      if (fournisseurError) throw fournisseurError;

      toast.success('Fournisseur supprimé');
      await loadAllPatrimoine(); // Recharger toutes les données
      await buildMatrix();

    } catch (error) {
      console.error('Erreur lors de la suppression du fournisseur:', error);
      toast.error('Erreur lors de la suppression');
    }
  };

  // Renommer un fournisseur
  const handleRenameFournisseur = async (fournisseurId: string, oldName: string, newName: string) => {
    if (!newName.trim() || newName === oldName) return;

    try {
      // Mettre à jour le nom du fournisseur
      const { error: fournisseurError } = await supabase
        .from('patrimoine_fournisseurs')
        .update({ nom: newName.trim() })
        .eq('id', fournisseurId);

      if (fournisseurError) throw fournisseurError;

      // Mettre à jour toutes les entrées de patrimoine associées
      const { error: patrimoineError } = await supabase
        .from('client_patrimoine')
        .update({ fournisseur: newName.trim() })
        .eq('fournisseur', oldName);

      if (patrimoineError) throw patrimoineError;

      toast.success('Fournisseur renommé');
      setEditingFournisseur(null);
      await loadAllPatrimoine(); // Recharger toutes les données
      await buildMatrix();

    } catch (error) {
      console.error('Erreur lors du renommage du fournisseur:', error);
      toast.error('Erreur lors du renommage');
    }
  };

  // Charger les données au montage et quand la dialog s'ouvre
  useEffect(() => {
    if (isOpen) {
      loadAllPatrimoine();
    }
  }, [isOpen, loadAllPatrimoine]);

  // Reconstruire la matrice quand les données changent
  useEffect(() => {
    if (entries.length >= 0 && clients.length > 0) {
      buildMatrix();
    }
  }, [entries, clients]);

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              Tableau Global du Patrimoine
            </DialogTitle>
          </div>
          <DialogDescription>
            Vue d'ensemble de la répartition du patrimoine par client et fournisseur avec totaux consolidés.
          </DialogDescription>
          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center gap-3">
              <Button
                onClick={() => setShowDashboard(true)}
                variant="outline"
                size="sm"
                className="border-green-200 text-green-700 hover:bg-green-50"
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                Évolution Temporelle
              </Button>
              <Button
                onClick={() => setShowAnalytics(true)}
                variant="outline"
                size="sm"
                className="border-purple-200 text-purple-700 hover:bg-purple-50"
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                Analyse Approfondie
              </Button>
              <Button
                onClick={() => setShowFournisseurManager(true)}
                variant="outline"
                size="sm"
                className="border-blue-200 text-blue-700 hover:bg-blue-50"
              >
                <Settings className="h-4 w-4 mr-2" />
                Gérer Fournisseurs
              </Button>
              <Badge variant="secondary" className="text-lg px-3 py-1 bg-green-100 text-green-800">
                Total: {formatMontant(matrixData.grandTotal)}
              </Badge>
            </div>
          </div>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-500">Chargement du patrimoine global...</p>
            </div>
          </div>
        ) : (
          <div className="overflow-auto max-h-[70vh]">
            <Table>
              <TableHeader>
                <TableRow className="bg-gradient-to-r from-blue-50 to-indigo-50">
                  <TableHead className="font-semibold text-gray-700 py-3 px-4 min-w-[200px] sticky left-0 bg-blue-50 z-10">
                    <div className="flex items-center gap-2">
                      <Building2 className="h-4 w-4 text-blue-600" />
                      Clients
                    </div>
                  </TableHead>
                  {matrixData.fournisseurs.map((fournisseur) => (
                    <TableHead key={fournisseur.id} className="text-center py-3 px-2 min-w-[120px]">
                      <span className="font-semibold text-gray-700">{fournisseur.nom}</span>
                    </TableHead>
                  ))}
                  <TableHead className="text-center py-3 px-4 min-w-[120px] bg-green-50">
                    <div className="flex flex-col items-center gap-1">
                      <span className="font-semibold text-gray-700">Total</span>
                      <Euro className="h-4 w-4 text-green-600" />
                    </div>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {matrixData.clients.map((client) => (
                  <TableRow key={client.id} className="hover:bg-blue-50/50 transition-colors">
                    <TableCell className="font-medium py-3 px-4 sticky left-0 bg-white z-10 border-r">
                      <span className="text-gray-800">{client.name}</span>
                    </TableCell>
                    {matrixData.fournisseurs.map((fournisseur) => {
                      const cellValue = getCellValue(client.id, fournisseur.id);
                      const isEditing = editingCell?.clientId === client.id && editingCell?.fournisseurId === fournisseur.id;
                      
                      return (
                        <TableCell key={fournisseur.id} className="text-center py-3 px-2">
                          {isEditing ? (
                            <Input
                              type="number"
                              value={editingValue}
                              onChange={(e) => setEditingValue(e.target.value)}
                              className="w-24 text-center text-sm"
                              autoFocus
                              onBlur={handleSaveEdit}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  handleSaveEdit();
                                }
                                if (e.key === 'Escape') {
                                  handleCancelEdit();
                                }
                              }}
                            />
                          ) : (
                            <span
                              className={`cursor-pointer px-2 py-1 rounded text-sm transition-colors ${
                                cellValue > 0
                                  ? 'text-green-600 hover:bg-green-50 font-semibold'
                                  : 'text-gray-400 hover:bg-gray-50'
                              }`}
                              onClick={() => handleCellEdit(client.id, fournisseur.id, cellValue)}
                            >
                              {formatMontant(cellValue)}
                            </span>
                          )}
                        </TableCell>
                      );
                    })}
                    <TableCell className="text-center py-3 px-4 bg-green-50 font-semibold text-green-700">
                      {formatMontant(client.total)}
                    </TableCell>
                  </TableRow>
                ))}
                
                {/* Ligne des totaux */}
                <TableRow className="bg-green-50 border-t-2 border-green-200">
                  <TableCell className="font-bold py-3 px-4 sticky left-0 bg-green-50 z-10">
                    TOTAL GÉNÉRAL
                  </TableCell>
                  {matrixData.fournisseurs.map((fournisseur) => (
                    <TableCell key={fournisseur.id} className="text-center py-3 px-2 font-semibold text-green-700">
                      {formatMontant(fournisseur.total)}
                    </TableCell>
                  ))}
                  <TableCell className="text-center py-3 px-4 font-bold text-green-800 text-lg">
                    {formatMontant(matrixData.grandTotal)}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        )}
      </DialogContent>

      {/* Dialog de gestion des fournisseurs */}
      <Dialog open={showFournisseurManager} onOpenChange={setShowFournisseurManager}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5 text-blue-600" />
              Gestion des Fournisseurs
            </DialogTitle>
            <DialogDescription>
              Gérer la liste des fournisseurs de patrimoine : ajouter, modifier ou supprimer des fournisseurs.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Ajouter un nouveau fournisseur */}
            <div className="flex gap-3">
              <Input
                placeholder="Nom du nouveau fournisseur..."
                value={newFournisseurName}
                onChange={(e) => setNewFournisseurName(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleAddFournisseur()}
                className="flex-1"
              />
              <Button
                onClick={handleAddFournisseur}
                disabled={!newFournisseurName.trim()}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Ajouter
              </Button>
            </div>

            {/* Liste des fournisseurs existants */}
            <div className="space-y-2 max-h-96 overflow-y-auto">
              <h3 className="font-medium text-gray-700 mb-3">Fournisseurs existants</h3>
              {matrixData.fournisseurs.map((fournisseur) => (
                <div key={fournisseur.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    {editingFournisseur?.id === fournisseur.id ? (
                      <Input
                        value={editingFournisseur.nom}
                        onChange={(e) => setEditingFournisseur({ ...editingFournisseur, nom: e.target.value })}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            handleRenameFournisseur(fournisseur.id, fournisseur.nom, editingFournisseur.nom);
                          }
                          if (e.key === 'Escape') {
                            setEditingFournisseur(null);
                          }
                        }}
                        onBlur={() => handleRenameFournisseur(fournisseur.id, fournisseur.nom, editingFournisseur.nom)}
                        className="w-48"
                        autoFocus
                      />
                    ) : (
                      <span
                        className="font-medium cursor-pointer hover:text-blue-600"
                        onClick={() => setEditingFournisseur({ id: fournisseur.id, nom: fournisseur.nom })}
                      >
                        {fournisseur.nom}
                      </span>
                    )}
                    <Badge variant="outline" className="bg-green-50 text-green-700">
                      {formatMontant(fournisseur.total)}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setEditingFournisseur({ id: fournisseur.id, nom: fournisseur.nom })}
                      className="text-blue-600 hover:bg-blue-50"
                    >
                      <Edit3 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteFournisseur(fournisseur.id, fournisseur.nom)}
                      className="text-red-600 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Dialog d'analyse approfondie */}
      <PatrimoineAnalytics
        isOpen={showAnalytics}
        onClose={() => setShowAnalytics(false)}
      />

      {/* Dashboard d'évolution temporelle */}
      <PatrimoineDashboard
        isOpen={showDashboard}
        onClose={() => setShowDashboard(false)}
      />
    </Dialog>
  );
};
