import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card } from '@/components/ui/card';
import { Plus, X, Edit3, Check, GripVertical } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useProfileContext } from '@/contexts/ProfileContext';
import { encryptText, safeDecrypt } from '@/utils/encryption';
import { toast } from 'sonner';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import {
  useSortable
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface ClientNote {
  id: string;
  content: string;
  created_at: string;
  updated_at: string;
  position: number;
  created_by: string;
  author_name?: string;
}

interface FreeNotesTabProps {
  clientId: string;
}

interface SortableNoteProps {
  note: ClientNote;
  onEdit: (id: string, content: string) => void;
  onDelete: (id: string) => void;
}

const SortableNote: React.FC<SortableNoteProps> = ({ note, onEdit, onDelete }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(note.content);
  const inputRef = useRef<HTMLInputElement>(null);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: note.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const handleEdit = () => {
    setIsEditing(true);
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
        inputRef.current.setSelectionRange(editContent.length, editContent.length);
      }
    }, 0);
  };

  const handleSave = () => {
    if (editContent.trim() !== note.content) {
      onEdit(note.id, editContent.trim());
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditContent(note.content);
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  // Fonction pour obtenir les initiales
  const getInitials = (name: string) => {
    if (!name) return '?';
    const words = name.trim().split(' ');
    if (words.length === 1) return words[0].charAt(0).toUpperCase();
    return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="group flex items-start gap-3 py-2.5 px-3 hover:bg-gray-50 rounded-md transition-colors"
    >
      <div
        {...attributes}
        {...listeners}
        className="cursor-grab active:cursor-grabbing text-gray-300 hover:text-gray-500 mt-1.5"
      >
        <GripVertical size={12} />
      </div>

      {/* Avatar avec initiales - Design moderne */}
      <div className="flex-shrink-0 w-6 h-6 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mt-1 shadow-sm">
        <span className="text-xs font-semibold text-white">
          {getInitials(note.author_name || '')}
        </span>
      </div>

      {/* Contenu principal */}
      <div className="flex-1 min-w-0">
        {isEditing ? (
          <Input
            ref={inputRef}
            value={editContent}
            onChange={(e) => setEditContent(e.target.value)}
            onKeyDown={handleKeyDown}
            onBlur={handleSave}
            className="w-full h-8 text-base border-none shadow-none focus:ring-1 focus:ring-blue-500 bg-white rounded-md px-2"
            placeholder="Écrivez votre note..."
          />
        ) : (
          <>
            <div
              className="text-base text-gray-800 cursor-pointer leading-relaxed mb-1.5 break-words"
              onClick={handleEdit}
            >
              {note.content}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-400 font-medium">
                {new Date(note.updated_at).toLocaleDateString('fr-FR', {
                  day: '2-digit',
                  month: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </span>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => onDelete(note.id)}
                className="opacity-0 group-hover:opacity-100 h-5 w-5 p-0 text-gray-400 hover:text-red-500 rounded-full"
              >
                <X size={12} />
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export const FreeNotesTab: React.FC<FreeNotesTabProps> = ({ clientId }) => {
  const [notes, setNotes] = useState<ClientNote[]>([]);
  const [newNote, setNewNote] = useState('');
  const [loading, setLoading] = useState(false);
  const { currentProfile } = useProfileContext();
  const newNoteRef = useRef<HTMLInputElement>(null);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Charger les notes
  const loadNotes = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('client_notes')
        .select(`
          *,
          assignees!client_notes_created_by_fkey(name)
        `)
        .eq('client_id', clientId)
        .order('created_at', { ascending: false }); // Nouvelles notes en haut

      if (error) throw error;

      // Déchiffrer les notes et ajouter le nom de l'auteur
      const decryptedNotes = await Promise.all(
        (data || []).map(async (note) => ({
          ...note,
          content: await safeDecrypt(note.content),
          author_name: note.assignees?.name || 'Utilisateur'
        }))
      );

      setNotes(decryptedNotes);
    } catch (error) {
      console.error('Erreur lors du chargement des notes:', error);
      toast.error('Erreur lors du chargement des notes');
    } finally {
      setLoading(false);
    }
  };

  // Ajouter une nouvelle note
  const addNote = async () => {
    if (!newNote.trim() || !currentProfile) return;

    try {
      const encryptedContent = await encryptText(newNote.trim());

      const { data, error } = await supabase
        .from('client_notes')
        .insert({
          client_id: clientId,
          content: encryptedContent,
          created_by: currentProfile.id,
          position: 0 // Nouvelle note en position 0
        })
        .select(`
          *,
          assignees!client_notes_created_by_fkey(name)
        `)
        .single();

      if (error) throw error;

      const newNoteDecrypted = {
        ...data,
        content: newNote.trim(),
        author_name: data.assignees?.name || currentProfile.name || 'Utilisateur'
      };

      // Ajouter en haut de la liste
      setNotes(prev => [newNoteDecrypted, ...prev]);
      setNewNote('');

      if (newNoteRef.current) {
        newNoteRef.current.focus();
      }

      toast.success('Note ajoutée');
    } catch (error) {
      console.error('Erreur lors de l\'ajout de la note:', error);
      toast.error('Erreur lors de l\'ajout de la note');
    }
  };

  // Modifier une note
  const editNote = async (id: string, content: string) => {
    try {
      const encryptedContent = await encryptText(content);

      const { error } = await supabase
        .from('client_notes')
        .update({ content: encryptedContent })
        .eq('id', id);

      if (error) throw error;

      setNotes(prev =>
        prev.map(note =>
          note.id === id
            ? { ...note, content, updated_at: new Date().toISOString() }
            : note
        )
      );

      toast.success('Note modifiée');
    } catch (error) {
      console.error('Erreur lors de la modification de la note:', error);
      toast.error('Erreur lors de la modification de la note');
    }
  };

  // Supprimer une note
  const deleteNote = async (id: string) => {
    try {
      const { error } = await supabase
        .from('client_notes')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setNotes(prev => prev.filter(note => note.id !== id));
      toast.success('Note supprimée');
    } catch (error) {
      console.error('Erreur lors de la suppression de la note:', error);
      toast.error('Erreur lors de la suppression de la note');
    }
  };

  // Gérer le drag & drop
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = notes.findIndex(note => note.id === active.id);
      const newIndex = notes.findIndex(note => note.id === over.id);

      const newNotes = arrayMove(notes, oldIndex, newIndex);
      setNotes(newNotes);

      // Mettre à jour les positions en base
      try {
        const updates = newNotes.map((note, index) => ({
          id: note.id,
          position: index
        }));

        for (const update of updates) {
          await supabase
            .from('client_notes')
            .update({ position: update.position })
            .eq('id', update.id);
        }
      } catch (error) {
        console.error('Erreur lors de la réorganisation:', error);
        toast.error('Erreur lors de la réorganisation');
        loadNotes(); // Recharger en cas d'erreur
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      addNote();
    }
  };

  useEffect(() => {
    loadNotes();
  }, [clientId]);

  return (
    <div className="space-y-3">
      {/* Zone d'ajout de note - Design moderne */}
      <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
        <div className="flex-shrink-0 w-6 h-6 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-sm">
          <span className="text-xs font-semibold text-white">
            {currentProfile?.name ? currentProfile.name.charAt(0).toUpperCase() : '+'}
          </span>
        </div>
        <Input
          ref={newNoteRef}
          value={newNote}
          onChange={(e) => setNewNote(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Ajouter une note... (Entrée pour sauvegarder)"
          className="flex-1 h-8 text-base border-none shadow-none bg-transparent focus:ring-1 focus:ring-blue-500"
        />
      </div>

      {/* Liste des notes - Design amélioré */}
      {loading ? (
        <div className="text-center py-4 text-gray-400 text-sm">
          Chargement...
        </div>
      ) : notes.length === 0 ? (
        <div className="text-center py-8 text-gray-400">
          <p className="text-sm">Aucune note</p>
          <p className="text-xs mt-1">Commencez par ajouter votre première note ci-dessus</p>
        </div>
      ) : (
        <div className="space-y-0">
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext items={notes.map(n => n.id)} strategy={verticalListSortingStrategy}>
              {notes.map((note, index) => (
                <div key={note.id}>
                  <SortableNote
                    note={note}
                    onEdit={editNote}
                    onDelete={deleteNote}
                  />
                  {/* Séparateur subtil entre les notes */}
                  {index < notes.length - 1 && (
                    <div className="h-px bg-gray-100 mx-9 my-1"></div>
                  )}
                </div>
              ))}
            </SortableContext>
          </DndContext>
        </div>
      )}
    </div>
  );
};
